
02e37871b495226e78c6da1e1dfc275e32c87352	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.1fe13fd5fdfcdad8fcb2.hot-update.js\",\"contentHash\":\"7d412c984f474dce923100c0e47b7acb\"}","integrity":"sha512-AVxUeLtpR3dBnqFSpAG/CgS15KQMzcBowRDZkJ7KeyRC2Qa42xadhwPROFYO7qy52/qAIU6JYZRokeD3ORduuw==","time":1754376398275,"size":93608}