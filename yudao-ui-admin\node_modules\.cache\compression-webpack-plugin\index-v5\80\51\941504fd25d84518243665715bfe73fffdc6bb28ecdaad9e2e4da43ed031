
038b3a35c1b685fd8417b1133738f1d6f63a6668	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"1e8684d241b0d03192aea04788ff7412\"}","integrity":"sha512-5qvkhuB6nyTcOTUvuUngaFwg4b8iTO0ZvSO4x0QeVl4D1TvLRNXnTQgyNPB0J2O9tKfCYaq6wQlnQ3KNMQqH3g==","time":1754376398729,"size":3474350}