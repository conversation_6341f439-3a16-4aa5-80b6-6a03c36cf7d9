
668cfa0fb80472539da490e358c5f602b22e90b6	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"5277516d8f6627eff378c8ef2c36daae\"}","integrity":"sha512-M5YXOmDlwKOZaYYLe/E1CXWwmuYjn+x8nG3Ppkth6VvA5SHef2HTJ2b56EZ7+SlAFJABO7+puXP7UzUWCF0zWQ==","time":1754376399858,"size":16762572}