{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue", "mtime": 1754376393695}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBjcmVhdGVXb3JrT3JkZXIsDQogIHVwZGF0ZVdvcmtPcmRlciwNCiAgZGVsZXRlV29ya09yZGVyLA0KICBnZXRXb3JrT3JkZXIsDQogIGdldFdvcmtPcmRlclBhZ2UsDQogIGV4cG9ydFdvcmtPcmRlckV4Y2VsLA0KICB0YWtlV29ya09yZGVyLA0KICBob3NwaXRhbENoZWNrLA0KICBwcm9jZXNzIGFzIHByb2Nlc3MyLA0KICB2aXNpdCwNCiAgcmVqZWN0LA0KICBkZWxheSwNCiAgcmV0dXJuV29ya09yZGVyLA0KICBnZXRIb3NwaXRhbE5hbWVzLA0KICBjcmVhdGVQZGYsDQogIGdldEJhdGNoT3BlcmF0aW9uTG9nUGFnZSwNCiAgYmF0Y2hSZWplY3RXb3JrT3JkZXJzDQp9DQpmcm9tICJAL2FwaS9pbnN1cmFuY2Uvd29ya09yZGVyIjsNCmltcG9ydCB7Z2V0U3VwcGxlbWVudGFyeUZpbGVSZWNvcmR9IGZyb20gIkAvYXBpL2luc3VyYW5jZS9zdXBwbGVtZW50YXJ5RmlsZVJlY29yZCI7DQppbXBvcnQgSW1hZ2VVcGxvYWQgZnJvbSAnQC9jb21wb25lbnRzL0ltYWdlVXBsb2FkJzsNCmltcG9ydCB7IGNoZWNrUGVybWksIGNoZWNrUm9sZSB9IGZyb20gIkAvdXRpbHMvcGVybWlzc2lvbiI7DQppbXBvcnQgV29ya09yZGVyRGV0YWlsIGZyb20gIi4vZGV0YWlsIg0KaW1wb3J0IGVjYXJkIGZyb20gJy4uL2NvbXBvbmVudHMvZWNhcmQudnVlJzsNCmltcG9ydCBlc2lnbiBmcm9tICcuLi9jb21wb25lbnRzL2VzaWduYXR1cmUudnVlJzsNCmltcG9ydCBvcmRlciBmcm9tICcuLi9jb21wb25lbnRzL29yZGVyLnZ1ZSc7DQppbXBvcnQgZGlzYWJsZVBlcnNvbiBmcm9tICcuLi9jb21wb25lbnRzL2Rpc2FibGVQZXJzb24udnVlJzsNCmltcG9ydCBob3VzZWhvbGQgZnJvbSAnLi4vY29tcG9uZW50cy9ob3VzZWhvbGQudnVlJzsNCmltcG9ydCBiYW5rQ2FyZCBmcm9tICcuLi9jb21wb25lbnRzL2JhbmtDYXJkLnZ1ZSc7DQppbXBvcnQgcGVyc29uSW5mbyBmcm9tICcuLi9jb21wb25lbnRzL3BlcnNvbkluZm8udnVlJzsNCmltcG9ydCBDb21wYW55U2VsZWN0IGZyb20gJy4uL2NvbXBhbnkvY29tcG9uZW50cy9jb21wYW55U2VsZWN0LnZ1ZSc7DQppbXBvcnQge2dldEJhc2VIZWFkZXJ9IGZyb20gIkAvdXRpbHMvcmVxdWVzdCI7DQpjb25zdCBDT05GSVJNX1RFWFQgPSAn56GuIOWumic7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJXb3JrT3JkZXIiLA0KICBjb21wb25lbnRzOiB7DQogICAgSW1hZ2VVcGxvYWQsDQogICAgV29ya09yZGVyRGV0YWlsLA0KICAgIGVjYXJkLA0KICAgIGVzaWduLA0KICAgIG9yZGVyLA0KICAgIGhvdXNlaG9sZCwNCiAgICBiYW5rQ2FyZCwNCiAgICBwZXJzb25JbmZvLA0KICAgIGRpc2FibGVQZXJzb24sDQogICAgQ29tcGFueVNlbGVjdCwNCiAgfSwNCiAgZGF0YSgpIHsNCg0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDlr7zlh7rpga7nvanlsYINCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOW3peWNleWIl+ihqA0KICAgICAgbGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICBkYXRlUmFuZ2VDb21wZW5zYXRpbmdEYXRlOiBbXSwNCiAgICAgIGRhdGVSYW5nZUNyZWF0ZVRpbWU6IFtdLA0KICAgICAgZGF0ZVJhbmdlVHJlYXRtZW50RGF0ZXRpbWU6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTm86IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdHlwZTogbnVsbCwNCiAgICAgICAgbmFtZTogbnVsbCwNCiAgICAgICAgaWRDYXJkTnVtYmVyOiBudWxsLA0KICAgICAgICBtb2JpbGVQaG9uZU51bWJlcjogbnVsbCwNCiAgICAgICAgdHJlYXRtZW50U2VyaWFsTnVtYmVyVHlwZTogdGhpcy5jYW5Jbml0aWFsRmlsdGVyKCk/ICcxJzogbnVsbCwNCiAgICAgICAgY29tcGxldGVTdGF0dXM6IHRoaXMuY2FuSW5pdGlhbEZpbHRlcigpPyAnMCc6IG51bGwsDQogICAgICAgIGhvc3BpdGFsTmFtZTogbnVsbCwNCiAgICAgICAgY29tcGFueUlkOiBudWxsLA0KICAgICAgICB0eXBlczogW10sDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgbmFtZTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLplb/ogIXlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICAgIGlkQ2FyZE51bWJlcjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLouqvku73or4Hlj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICB9LA0KICAgICAgZGV0YWlsSWQ6IHVuZGVmaW5lZCwNCiAgICAgIGRldGFpbFRpdGxlOiB1bmRlZmluZWQsDQogICAgICBkZXRhaWxPcGVuOiBmYWxzZSwNCiAgICAgIGRldGFpbE9wZW4yOiBmYWxzZSwNCiAgICAgIG1ldGhvZDogdW5kZWZpbmVkLA0KICAgICAgZWNhcmRPcGVuOiBmYWxzZSwNCiAgICAgIGVzaWduT3BlbjogZmFsc2UsDQogICAgICBvcmRlck9wZW46IGZhbHNlLA0KICAgICAgZGlzYWJsZVBlcnNvbjogew0KICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgaWROdW06IHVuZGVmaW5lZCwNCiAgICAgIH0sDQogICAgICBlY2FyZDogew0KICAgICAgICBpZE51bTogdW5kZWZpbmVkLA0KICAgICAgICBuYW1lOiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICBjb25maXJtQnRuVGV4dDogQ09ORklSTV9URVhULA0KICAgICAgY29uZmlybUJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgaG91c2VIb2xkT3BlbjogZmFsc2UsDQogICAgICBob3VzZWhvbGQ6IHsNCiAgICAgICAgaWROdW06IHVuZGVmaW5lZCwNCiAgICAgIH0sDQogICAgICBvcFR5cGU6IHVuZGVmaW5lZCwNCiAgICAgIGlkTnVtOiB1bmRlZmluZWQsDQogICAgICBiYW5rQWNjb3VudDogew0KICAgICAgICBiYW5rQWNjb3VudE9wZW46IGZhbHNlLA0KICAgICAgICBpZE51bTogdW5kZWZpbmVkDQogICAgICB9LA0KICAgICAgcGVyc29uSW5mbzogew0KICAgICAgICBvcGVuOiBmYWxzZSwNCiAgICAgICAgaWROdW06IHVuZGVmaW5lZA0KICAgICAgfSwNCiAgICAgIGhvc3BpdGFsTmFtZXM6IFtdLA0KICAgICAgLy/lt7LotZTku5jlt6XljZXlr7zlhaUNCiAgICAgIHVwbG9hZDogew0KICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjnlKjmiLflr7zlhaXvvIkNCiAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOeUqOaIt+WvvOWFpe+8iQ0KICAgICAgICB0aXRsZTogIiIsDQogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oA0KICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsDQogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqA0KICAgICAgICBoZWFkZXJzOiBnZXRCYXNlSGVhZGVyKCksDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2FkbWluLWFwaS9pbnN1cmFuY2Uvd29yay1vcmRlci9pbXBvcnQtc2V0dGxlbWVudC13b3JrLW9yZGVyJywNCiAgICAgICAgY2xhaW1BbW91bnRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2FkbWluLWFwaS9pbnN1cmFuY2Uvd29yay1vcmRlci9pbXBvcnQtY2xhaW0tYW1vdW50LXdvcmstb3JkZXInLA0KICAgICAgICB3YW5kYUltcG9ydFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvYWRtaW4tYXBpL2luc3VyYW5jZS93b3JrLW9yZGVyL2ltcG9ydC13YW5kYS1kYXRhJw0KICAgICAgfSwNCiAgICAgIHN1cHBsZW1lbnRhcnk6IHsNCiAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgIGFjdGl2ZVRhYjogJycsDQogICAgICAgIGZpbGVzOiB7fSwNCiAgICAgIH0sDQogICAgICBzdXBwbGVtZW50YXJ5VHlwZU1hcDogew0KICAgICAgICBNRURJQ0FMX0RJQUdOT1NJU19QUk9PRjogJ+WMu+eWl+iviuaWreivgeaYjicsDQogICAgICAgIE1FRElDQUxfRkVFX0lOVk9JQ0U6ICfljLvnlpfotLnnlKjlj5HnpagnLA0KICAgICAgICBEUlVHX0xJU1Q6ICfnlKjoja/muIXljZUnLA0KICAgICAgICBNRURJQ0FMX1NFVFRMRU1FTlQ6ICfljLvkv53nu5PnrpfljZUnLA0KICAgICAgICBPVVRQQVRJRU5UX01FRElDQUxfUkVDT1JEUzogJ+mXqOiviueXheWOhijpl6jor4opJywNCiAgICAgICAgRElTQ0hBUkdFX1JFQ09SRDogJ+WHuumZouiusOW9lSjkvY/pmaIpJywNCiAgICAgICAgRElTQUJJTElUWV9DRVJUSUZJQ0FURTogJ+aui+eWvumJtOWumuaKpeWRiicsDQogICAgICAgIFRSQUZGSUNfQUNDSURFTlRfQ0VSVElGSUNBVEU6ICfkuqTpgJrkuovmlYXotKPku7vorqTlrprkuaYnDQogICAgICB9LA0KICAgICAgd2FuZGFJbXBvcnRMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGRldGFpbEZvcm06IHt9LA0KICAgICAgZmlsZVVybE1hcDoge30sDQogICAgICBhY3RpdmVUYWI6ICcnLA0KICAgICAgLy8g5om56YeP5pON5L2c5pel5b+X5oq95bGJDQogICAgICBiYXRjaExvZ0RyYXdlcjogew0KICAgICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAgIHRvdGFsOiAwLA0KICAgICAgICBsaXN0OiBbXSwNCiAgICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgICBwYWdlTm86IDEsDQogICAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICAgIG9wZXJhdGlvblR5cGU6IG51bGwsDQogICAgICAgICAgc3RhdHVzOiBudWxsLA0KICAgICAgICAgIG9wZXJhdG9yTmFtZTogbnVsbCwNCiAgICAgICAgICBiZWdpblN0YXJ0VGltZTogbnVsbCwNCiAgICAgICAgICBlbmRTdGFydFRpbWU6IG51bGwNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+aLkue7neWvueivneahhg0KICAgICAgYmF0Y2hSZWplY3REaWFsb2c6IHsNCiAgICAgICAgdmlzaWJsZTogZmFsc2UsDQogICAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICBmb3JtOiB7DQogICAgICAgICAgY3V0b2ZmRGF0ZTogbnVsbA0KICAgICAgICB9DQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBpZiAoY2hlY2tQZXJtaShbJ2luc3VyYW5jZTpvbGQtcGVvcGxlLWFjY2lkZW50LWluc3VyYW5jZTpxdWVyeSddKSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy50eXBlcy5wdXNoKDEpOw0KICAgIH0NCiAgICBpZiAoY2hlY2tQZXJtaShbJ2luc3VyYW5jZTpkaXNhYmxlZC1wZW9wbGUtaW5zdXJhbmNlOnF1ZXJ5J10pKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnR5cGVzLnB1c2goNyk7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnR5cGVzLnB1c2goOCk7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnR5cGVzLnB1c2goOSk7DQogICAgfQ0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIGdldEhvc3BpdGFsTmFtZXMoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMuaG9zcGl0YWxOYW1lcyA9IHJlc3BvbnNlLmRhdGE7DQogICAgfSkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNhbkluaXRpYWxGaWx0ZXIoKSB7DQogICAgICAvLyByZXR1cm4gdGhpcy5jaGVja1JvbGUoWydpbnN1cmFuY2UnLCAnbXonXSk7DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQsIGluZGV4LCByb3cpIHsNCiAgICAgIHN3aXRjaCAoY29tbWFuZCkgew0KICAgICAgICBjYXNlICdoYW5kbGVVcGRhdGUnOg0KICAgICAgICAgIHRoaXMuaGFuZGxlVXBkYXRlKHJvdyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ2hhbmRsZURlbGV0ZSc6DQogICAgICAgICAgdGhpcy5oYW5kbGVEZWxldGUocm93KTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnaGFuZGxlQ3JlYXRlUGRmJzoNCiAgICAgICAgICB0aGlzLmhhbmRsZUNyZWF0ZVBkZihyb3cpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIGJyZWFrOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivouWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgLy8g5aSE55CG5p+l6K+i5Y+C5pWwDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXR1cyA9IHRoaXMuZ2V0U3RhdHVzKCk7DQogICAgICBsZXQgcGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXN9Ow0KICAgICAgdGhpcy5hZGRCZWdpbkFuZEVuZFRpbWUocGFyYW1zLCB0aGlzLmRhdGVSYW5nZUNvbXBlbnNhdGluZ0RhdGUsICdjb21wZW5zYXRpbmdEYXRlJyk7DQogICAgICB0aGlzLmFkZEJlZ2luQW5kRW5kVGltZShwYXJhbXMsIHRoaXMuZGF0ZVJhbmdlQ3JlYXRlVGltZSwgJ2NyZWF0ZVRpbWUnKTsNCiAgICAgIHRoaXMuYWRkQmVnaW5BbmRFbmRUaW1lKHBhcmFtcywgdGhpcy5kYXRlUmFuZ2VUcmVhdG1lbnREYXRldGltZSwgJ3RyZWF0bWVudERhdGV0aW1lJyk7DQogICAgICAvLyDmiafooYzmn6Xor6INCiAgICAgIGdldFdvcmtPcmRlclBhZ2UocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0Ow0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFN0YXR1cygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRyb3V0ZS5xdWVyeS5zdGF0dXMNCiAgICB9LA0KICAgIC8qKiDlj5bmtojmjInpkq4gKi8NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5kZXRhaWxPcGVuMiA9IGZhbHNlOw0KICAgICAgdGhpcy5lc2lnbk9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8qKiDooajljZXph43nva4gKi8NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgbmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBpZENhcmROdW1iZXI6IHVuZGVmaW5lZCwNCiAgICAgICAgbW9iaWxlUGhvbmVOdW1iZXI6IHVuZGVmaW5lZCwNCiAgICAgICAgYWRkcmVzczogdW5kZWZpbmVkLA0KICAgICAgICBob3NwaXRhbE5hbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgaW52b2ljZTogdW5kZWZpbmVkLA0KICAgICAgICBiaWxsOiB1bmRlZmluZWQsDQogICAgICAgIG1lZGljYWxSZWNvcmQ6IHVuZGVmaW5lZCwNCiAgICAgICAgc3VtbWFyeTogdW5kZWZpbmVkLA0KICAgICAgICBkaWFnbm9zZTogdW5kZWZpbmVkLA0KICAgICAgICBkaXNhYmlsaXR5UmVwb3J0OiB1bmRlZmluZWQsDQogICAgICAgIGRlYXRoUHJvb2Y6IHVuZGVmaW5lZCwNCiAgICAgICAgYWR2aWNlTW9uZXk6IHVuZGVmaW5lZCwNCiAgICAgICAgc3VnZ2VzdENvbXBlbnNhdGluZ01vbmV5OiB1bmRlZmluZWQsDQogICAgICAgIGFjdHVhbE1vbmV5OiB1bmRlZmluZWQsDQogICAgICAgIGNvbXBlbnNhdGluZ0RhdGU6IHVuZGVmaW5lZCwNCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU5vID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZUNvbXBlbnNhdGluZ0RhdGUgPSBbXTsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlQ3JlYXRlVGltZSA9IFtdOw0KICAgICAgdGhpcy5kYXRlUmFuZ2VUcmVhdG1lbnREYXRldGltZSA9IFtdOw0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOW3peWNlSI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaWQgPSByb3cuaWQ7DQogICAgICBnZXRXb3JrT3JkZXIoaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueW3peWNlSI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUNyZWF0ZVBkZihyb3cpIHsNCiAgICAgIGNyZWF0ZVBkZihyb3cuaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgdGhpcy5kZXRhaWxJZCA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB0aGlzLmRldGFpbFRpdGxlID0gIuafpeeci+ivpuaDhSI7DQogICAgfSwNCiAgICBoYW5kbGVEZXRhaWwyKHJvdykgew0KICAgICAgZ2V0U3VwcGxlbWVudGFyeUZpbGVSZWNvcmQocm93LnN1cHBsZW1lbnRhcnlGaWxlUmVjb3JkSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmRldGFpbEZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuZmlsZVVybE1hcCA9IHRoaXMuZGV0YWlsRm9ybS5maWxlVXJscyA/IEpTT04ucGFyc2UodGhpcy5kZXRhaWxGb3JtLmZpbGVVcmxzKSA6IHt9Ow0KICAgICAgICAgIC8vIOi/h+a7pOaOieayoeacieWbvueJh+eahOexu+Weiw0KICAgICAgICAgIHRoaXMuZmlsZVVybE1hcCA9IE9iamVjdC5mcm9tRW50cmllcygNCiAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKHRoaXMuZmlsZVVybE1hcCkuZmlsdGVyKChbXywgdXJsc10pID0+IHVybHMgJiYgdXJscy5sZW5ndGggPiAwKQ0KICAgICAgICAgICk7DQoNCiAgICAgICAgICBpZiAoT2JqZWN0LmtleXModGhpcy5maWxlVXJsTWFwKS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDorr7nva7nrKzkuIDkuKrmoIfnrb7kuLrmv4DmtLvnirbmgIENCiAgICAgICAgICAgIHRoaXMuYWN0aXZlVGFiID0gT2JqZWN0LmtleXModGhpcy5maWxlVXJsTWFwKVswXTsNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpAgZmlsZVVybHMg5aSx6LSlOicsIGUpOw0KICAgICAgICAgIHRoaXMuZmlsZVVybE1hcCA9IHt9Ow0KICAgICAgICB9DQogICAgICAgIHRoaXMuZGV0YWlsT3BlbjIgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBkb0FjdGlvbigpIHsNCiAgICAgIGxldCBwID0gdW5kZWZpbmVkOw0KICAgICAgc3dpdGNoKHRoaXMubWV0aG9kKSB7DQogICAgICAgIGNhc2UgJ3Rha2UnOg0KICAgICAgICAgIHRoaXMuY29uZmlybUJ0blRleHQgPSAn5q2j5Zyo562+56ugLOivt+eojeWAmSc7DQogICAgICAgICAgdGhpcy5jb25maXJtQnRuTG9hZGluZyA9IHRydWU7DQogICAgICAgICAgcCA9IHRha2VXb3JrT3JkZXIodGhpcy5kZXRhaWxJZCk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ2hvc3BpdGFsJzoNCiAgICAgICAgICBwID0gaG9zcGl0YWxDaGVjayh0aGlzLmRldGFpbElkKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAncHJvY2Vzcyc6DQogICAgICAgICAgcCA9IHByb2Nlc3MyKHRoaXMuZGV0YWlsSWQpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICd2aXNpdCc6DQogICAgICAgICAgbGV0IHN1Ym1pdERhdGEgPSB0aGlzLiRyZWZzLndvcmtPcmRlckRldGFpbC5nZXRTdWJtaXREYXRhKCkNCiAgICAgICAgICBzdWJtaXREYXRhLmlkID0gdGhpcy5kZXRhaWxJZA0KICAgICAgICAgIHAgPSB2aXNpdChzdWJtaXREYXRhKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAncmVqZWN0JzoNCiAgICAgICAgICBsZXQgcmVqZWN0RGF0YSA9IHsNCiAgICAgICAgICAgIGlkOiB0aGlzLmRldGFpbElkLA0KICAgICAgICAgICAgcmVtYXJrOiB0aGlzLiRyZWZzLndvcmtPcmRlckRldGFpbC5nZXRTdWJtaXREYXRhKCkucmVtYXJrDQogICAgICAgICAgfQ0KICAgICAgICAgIHAgPSByZWplY3QocmVqZWN0RGF0YSk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ2RlbGF5JzoNCiAgICAgICAgICBsZXQgZGVsYXlEYXRhID0gew0KICAgICAgICAgICAgaWQ6IHRoaXMuZGV0YWlsSWQsDQogICAgICAgICAgICByZW1hcms6IHRoaXMuJHJlZnMud29ya09yZGVyRGV0YWlsLmdldFN1Ym1pdERhdGEoKS5yZW1hcmsNCiAgICAgICAgICB9DQogICAgICAgICAgcCA9IGRlbGF5KGRlbGF5RGF0YSk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aJvuS4jeWIsOWvueW6lOaWueazlTogJyArIHRoaXMubWV0aG9kKTsNCiAgICAgIH0NCiAgICAgIHAudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuY29uZmlybUJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5jb25maXJtQnRuVGV4dCA9IENPTkZJUk1fVEVYVDsNCiAgICAgICAgdGhpcy5jYW5jZWwoKTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZVRha2Uocm93KSB7DQogICAgICB0aGlzLmRldGFpbElkID0gcm93LmlkOw0KICAgICAgdGhpcy5kZXRhaWxPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuZGV0YWlsVGl0bGUgPSAi5o6l5Y2VIjsNCiAgICAgIHRoaXMubWV0aG9kID0gJ3Rha2UnOw0KICAgICAgdGhpcy5jb25maXJtQnRuVGV4dCA9ICfmjqUg5Y2VJzsNCiAgICAgIHRoaXMub3BUeXBlID0gJ3Rha2UnOw0KICAgIH0sDQogICAgaGFuZGxlUmVqZWN0KHJvdykgew0KICAgICAgdGhpcy5kZXRhaWxJZCA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB0aGlzLmRldGFpbFRpdGxlID0gIuaLkue7nSI7DQogICAgICB0aGlzLm1ldGhvZCA9ICdyZWplY3QnOw0KICAgICAgdGhpcy5jb25maXJtQnRuVGV4dCA9ICfmi5Lnu50nOw0KICAgICAgdGhpcy5vcFR5cGUgPSAncmVqZWN0JzsNCiAgICB9LA0KICAgIGhhbmRsZURlbGF5KHJvdykgew0KICAgICAgdGhpcy5kZXRhaWxJZCA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB0aGlzLmRldGFpbFRpdGxlID0gIuW7tuWQjiI7DQogICAgICB0aGlzLm1ldGhvZCA9ICdkZWxheSc7DQogICAgICB0aGlzLmNvbmZpcm1CdG5UZXh0ID0gJ+W7tiDlkI4nOw0KICAgICAgdGhpcy5vcFR5cGUgPSAnZGVsYXknOw0KICAgIH0sDQogICAgaGFuZGxlSG9zcGl0YWxDaGVjayhyb3cpIHsNCiAgICAgIHRoaXMuZGV0YWlsSWQgPSByb3cuaWQ7DQogICAgICB0aGlzLmVzaWduT3BlbiA9IHRydWU7DQogICAgICB0aGlzLm1ldGhvZCA9ICdob3NwaXRhbCc7DQogICAgfSwNCiAgICBoYW5kbGVQcm9jZXNzKHJvdykgew0KICAgICAgdGhpcy5kZXRhaWxJZCA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7DQogICAgICB0aGlzLmRldGFpbFRpdGxlID0gIuWkhOeQhiI7DQogICAgICB0aGlzLm1ldGhvZCA9ICdwcm9jZXNzJzsNCiAgICAgIHRoaXMub3BUeXBlID0gJ3Byb2Nlc3MnOw0KDQogICAgfSwNCiAgICBoYW5kbGVWaXNpdChyb3cpIHsNCiAgICAgIHRoaXMuZGV0YWlsSWQgPSByb3cuaWQ7DQogICAgICB0aGlzLmRldGFpbE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5kZXRhaWxUaXRsZSA9ICLlm57orr8iOw0KICAgICAgdGhpcy5tZXRob2QgPSAndmlzaXQnOw0KICAgICAgdGhpcy5vcFR5cGUgPSAndmlzaXQnOw0KICAgIH0sDQogICAgaGFuZGxlUmV0dXJuKHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybShg5piv5ZCm56Gu6K6k5Zue6YCA5bel5Y2VKCR7cm93Lm5hbWV9KWApLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgICAgcmV0dXJuIHJldHVybldvcmtPcmRlcihyb3cuaWQpOw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlm57pgIDmiJDlip8iKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAoIXZhbGlkKSB7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIC8vIOS/ruaUueeahOaPkOS6pA0KICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICB1cGRhdGVXb3JrT3JkZXIodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgLy8g5re75Yqg55qE5o+Q5LqkDQogICAgICAgIGNyZWF0ZVdvcmtPcmRlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5bel5Y2V57yW5Y+35Li6IicgKyBpZCArICci55qE5pWw5o2u6aG5PycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgICAgcmV0dXJuIGRlbGV0ZVdvcmtPcmRlcihpZCk7DQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgLy8g5aSE55CG5p+l6K+i5Y+C5pWwDQogICAgICBsZXQgcGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXN9Ow0KICAgICAgcGFyYW1zLnBhZ2VObyA9IHVuZGVmaW5lZDsNCiAgICAgIHBhcmFtcy5wYWdlU2l6ZSA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMuYWRkQmVnaW5BbmRFbmRUaW1lKHBhcmFtcywgdGhpcy5kYXRlUmFuZ2VDb21wZW5zYXRpbmdEYXRlLCAnY29tcGVuc2F0aW5nRGF0ZScpOw0KICAgICAgdGhpcy5hZGRCZWdpbkFuZEVuZFRpbWUocGFyYW1zLCB0aGlzLmRhdGVSYW5nZUNyZWF0ZVRpbWUsICdjcmVhdGVUaW1lJyk7DQogICAgICB0aGlzLmFkZEJlZ2luQW5kRW5kVGltZShwYXJhbXMsIHRoaXMuZGF0ZVJhbmdlVHJlYXRtZW50RGF0ZXRpbWUsICd0cmVhdG1lbnREYXRldGltZScpOw0KICAgICAgLy8g5omn6KGM5a+85Ye6DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInlt6XljZXmlbDmja7pobk/JykudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICByZXR1cm4gZXhwb3J0V29ya09yZGVyRXhjZWwocGFyYW1zKTsNCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kZG93bmxvYWQuZXhjZWwocmVzcG9uc2UsICflt6XljZUueGxzJyk7DQogICAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGhhbmRsZUVjYXJkKHJvdykgew0KICAgICAgdGhpcy5lY2FyZE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5lY2FyZCA9IHsNCiAgICAgICAgaWROdW06IHJvdy5pZENhcmROdW1iZXIsDQogICAgICAgIG5hbWU6IHJvdy5uYW1lDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVPcmRlcihyb3cpIHsNCiAgICAgIHRoaXMub3JkZXJPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuaWROdW0gPSByb3cuaWRDYXJkTnVtYmVyDQogICAgfSwNCiAgICBoYW5kbGVEaXNhYmxlUGVyc29uKHJvdykgew0KICAgICAgdGhpcy5kaXNhYmxlUGVyc29uLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5kaXNhYmxlUGVyc29uLmlkTnVtID0gcm93LmlkQ2FyZE51bWJlcjsNCiAgICB9LA0KICAgIGhhbmRsZVJlY29yZChyb3cpIHsNCiAgICAgIHdpbmRvdy5vcGVuKHJvdy5wZGYuc2lnbmVkUGRmLCAiX2JsYW5rIiwgInJlc2l6YWJsZSxzY3JvbGxiYXJzLHN0YXR1cyIpOw0KICAgIH0sDQogICAgaGFuZGxlSG91c2VIb2xkKHJvdykgew0KICAgICAgdGhpcy5ob3VzZUhvbGRPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMuaG91c2Vob2xkID0gew0KICAgICAgICBpZE51bTogcm93LmlkQ2FyZE51bWJlciwNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUJhbmtDYXJkKHJvdykgew0KICAgICAgdGhpcy5iYW5rQWNjb3VudCA9IHsNCiAgICAgICAgYmFua0FjY291bnRPcGVuOiB0cnVlLA0KICAgICAgICBpZE51bTogcm93LmlkQ2FyZE51bWJlciwNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZXJQZXJzb25JbmZvKHJvdykgew0KICAgICAgdGhpcy5wZXJzb25JbmZvID0gew0KICAgICAgICBvcGVuOiB0cnVlLA0KICAgICAgICBpZE51bTogcm93LmlkQ2FyZE51bWJlciwNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoZWNrUGVybWksDQogICAgY2hlY2tSb2xlLA0KICAgIGhhbmRsZUltcG9ydCgpIHsNCiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIuW3peWNleWvvOWFpSI7DQogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudXBsb2FkLnVybCA9IHRoaXMudXBsb2FkLnVybDsNCiAgICB9LA0KICAgIGhhbmRsZUNsYWltQW1vdW50SW1wb3J0KCkgew0KICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi55CG6LWU6YeR6aKd5a+85YWlIjsNCiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy51cGxvYWQudXJsID0gdGhpcy51cGxvYWQuY2xhaW1BbW91bnRVcmw7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYNCiAgICBoYW5kbGVGaWxlVXBsb2FkUHJvZ3Jlc3MoZXZlbnQsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IHRydWU7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDmiJDlip/lpITnkIYNCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlICE9PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZykNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgbGV0IGZpbGVOYW1lID0gZmlsZS5uYW1lOw0KICAgICAgbGV0IGhyZWYgPSByZXNwb25zZS5kYXRhOw0KICAgICAgbGV0IGRvd25BID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgZG93bkEuaHJlZiA9IGhyZWY7DQogICAgICBkb3duQS5kb3dubG9hZCA9IGZpbGVOYW1lOw0KICAgICAgZG93bkEuY2xpY2soKTsNCiAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGhyZWYpOw0KICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWvvOWFpeaIkOWKn++8jOivt+afpeeci+WvvOWFpee7k+aenOaWh+S7tiIpOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YNCiAgICBzdWJtaXRGaWxlRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuihpeWFhei1hOaWmeaMiemSrueCueWHuyAqLw0KICAgIGhhbmRsZVN1cHBsZW1lbnRhcnkocm93KSB7DQogICAgICAvLyDlhYjojrflj5blt6XljZXor6bmg4UNCiAgICAgIGdldFdvcmtPcmRlcihyb3cuaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCB3b3JrT3JkZXIgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBpZiAoIXdvcmtPcmRlci5zdXBwbGVtZW50YXJ5RmlsZXMpIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5rKh5pyJ6KGl5YWF6LWE5paZIik7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQoNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCBmaWxlcyA9IEpTT04ucGFyc2Uod29ya09yZGVyLnN1cHBsZW1lbnRhcnlGaWxlcyk7DQogICAgICAgICAgLy8g6L+H5ruk5o6J5rKh5pyJ5Zu+54mH55qE57G75Z6LDQogICAgICAgICAgdGhpcy5zdXBwbGVtZW50YXJ5LmZpbGVzID0gT2JqZWN0LmZyb21FbnRyaWVzKA0KICAgICAgICAgICAgT2JqZWN0LmVudHJpZXMoZmlsZXMpLmZpbHRlcigoW18sIHVybHNdKSA9PiB1cmxzICYmIHVybHMubGVuZ3RoID4gMCkNCiAgICAgICAgICApOw0KDQogICAgICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc3VwcGxlbWVudGFyeS5maWxlcykubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5rKh5pyJ6KGl5YWF6LWE5paZIik7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6K6+572u56ys5LiA5Liq5qCH562+5Li65r+A5rS754q25oCBDQogICAgICAgICAgdGhpcy5zdXBwbGVtZW50YXJ5LmFjdGl2ZVRhYiA9IE9iamVjdC5rZXlzKHRoaXMuc3VwcGxlbWVudGFyeS5maWxlcylbMF07DQogICAgICAgICAgdGhpcy5zdXBwbGVtZW50YXJ5Lm9wZW4gPSB0cnVlOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi6Kej5p6Q6KGl5YWF6LWE5paZ5aSx6LSlIiwgZSk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuino+aekOihpeWFhei1hOaWmeWksei0pSIpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5blt6XljZXor6bmg4XlpLHotKUiKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluihpeWFhei1hOaWmeexu+Wei+eahOaYvuekuuaWh+acrCAqLw0KICAgIGdldFN1cHBsZW1lbnRhcnlUeXBlTGFiZWwodHlwZSkgew0KICAgICAgcmV0dXJuIHRoaXMuc3VwcGxlbWVudGFyeVR5cGVNYXBbdHlwZV0gfHwgdHlwZTsNCiAgICB9LA0KICAgIC8qKiDkuIfovr7mlbDmja7lr7zlhaXmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVXYW5kYUltcG9ydCgpIHsNCiAgICAgIHRoaXMudXBsb2FkLnRpdGxlID0gIuS4h+i+vuaVsOaNruWvvOWFpSI7DQogICAgICB0aGlzLnVwbG9hZC5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudXBsb2FkLnVybCA9IHRoaXMudXBsb2FkLndhbmRhSW1wb3J0VXJsOw0KICAgIH0sDQogICAgLyoqIOaJuemHj+aTjeS9nOaXpeW/l+aMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUJhdGNoT3BlcmF0aW9uTG9nKCkgew0KICAgICAgdGhpcy5iYXRjaExvZ0RyYXdlci52aXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuZ2V0QmF0Y2hMb2dMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5om56YeP5pON5L2c5pel5b+X5YiX6KGoICovDQogICAgZ2V0QmF0Y2hMb2dMaXN0KCkgew0KICAgICAgdGhpcy5iYXRjaExvZ0RyYXdlci5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIC8vIOWkhOeQhuaXtumXtOiMg+WbtOWPguaVsA0KICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5iYXRjaExvZ0RyYXdlci5xdWVyeVBhcmFtcyB9Ow0KICAgICAgaWYgKHRoaXMuYmF0Y2hMb2dEcmF3ZXIuZGF0ZVJhbmdlICYmIHRoaXMuYmF0Y2hMb2dEcmF3ZXIuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgew0KICAgICAgICBwYXJhbXMuYmVnaW5TdGFydFRpbWUgPSB0aGlzLmJhdGNoTG9nRHJhd2VyLmRhdGVSYW5nZVswXTsNCiAgICAgICAgcGFyYW1zLmVuZFN0YXJ0VGltZSA9IHRoaXMuYmF0Y2hMb2dEcmF3ZXIuZGF0ZVJhbmdlWzFdOw0KICAgICAgfQ0KDQogICAgICBnZXRCYXRjaE9wZXJhdGlvbkxvZ1BhZ2UocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5iYXRjaExvZ0RyYXdlci5saXN0ID0gcmVzcG9uc2UuZGF0YS5saXN0Ow0KICAgICAgICB0aGlzLmJhdGNoTG9nRHJhd2VyLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsNCiAgICAgICAgdGhpcy5iYXRjaExvZ0RyYXdlci5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuYmF0Y2hMb2dEcmF3ZXIubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6YeN572u5om56YeP5pON5L2c5pel5b+X5p+l6K+iICovDQogICAgcmVzZXRCYXRjaExvZ1F1ZXJ5KCkgew0KICAgICAgdGhpcy5iYXRjaExvZ0RyYXdlci5kYXRlUmFuZ2UgPSBbXTsNCiAgICAgIHRoaXMuYmF0Y2hMb2dEcmF3ZXIucXVlcnlQYXJhbXMgPSB7DQogICAgICAgIHBhZ2VObzogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBvcGVyYXRpb25UeXBlOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIG9wZXJhdG9yTmFtZTogbnVsbCwNCiAgICAgICAgYmVnaW5TdGFydFRpbWU6IG51bGwsDQogICAgICAgIGVuZFN0YXJ0VGltZTogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMuZ2V0QmF0Y2hMb2dMaXN0KCk7DQogICAgfSwNCiAgICAvKiog5YWz6Zet5om56YeP5pON5L2c5pel5b+X5oq95bGJICovDQogICAgaGFuZGxlQmF0Y2hMb2dEcmF3ZXJDbG9zZSgpIHsNCiAgICAgIHRoaXMuYmF0Y2hMb2dEcmF3ZXIudmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluaJuemHj+aTjeS9nOeKtuaAgeagh+etvuexu+WeiyAqLw0KICAgIGdldEJhdGNoTG9nU3RhdHVzVHlwZShzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgJ1JVTk5JTkcnOg0KICAgICAgICAgIHJldHVybiAnd2FybmluZyc7DQogICAgICAgIGNhc2UgJ0NPTVBMRVRFRCc6DQogICAgICAgICAgcmV0dXJuICdzdWNjZXNzJzsNCiAgICAgICAgY2FzZSAnRkFJTEVEJzoNCiAgICAgICAgICByZXR1cm4gJ2Rhbmdlcic7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuICdpbmZvJzsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJbmiafooYzml7bplb8gKi8NCiAgICBmb3JtYXREdXJhdGlvbihkdXJhdGlvbikgew0KICAgICAgaWYgKCFkdXJhdGlvbikgcmV0dXJuICctJzsNCg0KICAgICAgY29uc3Qgc2Vjb25kcyA9IE1hdGguZmxvb3IoZHVyYXRpb24gLyAxMDAwKTsNCiAgICAgIGNvbnN0IG1pbnV0ZXMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyA2MCk7DQogICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobWludXRlcyAvIDYwKTsNCg0KICAgICAgaWYgKGhvdXJzID4gMCkgew0KICAgICAgICByZXR1cm4gYCR7aG91cnN95bCP5pe2JHttaW51dGVzICUgNjB95YiGJHtzZWNvbmRzICUgNjB956eSYDsNCiAgICAgIH0gZWxzZSBpZiAobWludXRlcyA+IDApIHsNCiAgICAgICAgcmV0dXJuIGAke21pbnV0ZXN95YiGJHtzZWNvbmRzICUgNjB956eSYDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHtzZWNvbmRzfeenkmA7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5om56YeP5ouS57ud5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQmF0Y2hSZWplY3QoKSB7DQogICAgICB0aGlzLmJhdGNoUmVqZWN0RGlhbG9nLnZpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5iYXRjaFJlamVjdERpYWxvZy5mb3JtLmN1dG9mZkRhdGUgPSBudWxsOw0KICAgIH0sDQogICAgLyoqIOWFs+mXreaJuemHj+aLkue7neWvueivneahhiAqLw0KICAgIGhhbmRsZUJhdGNoUmVqZWN0RGlhbG9nQ2xvc2UoKSB7DQogICAgICB0aGlzLmJhdGNoUmVqZWN0RGlhbG9nLnZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuYmF0Y2hSZWplY3REaWFsb2cubG9hZGluZyA9IGZhbHNlOw0KICAgICAgdGhpcy5iYXRjaFJlamVjdERpYWxvZy5mb3JtLmN1dG9mZkRhdGUgPSBudWxsOw0KICAgICAgLy8g5riF6Zmk6KGo5Y2V6aqM6K+BDQogICAgICBpZiAodGhpcy4kcmVmcy5iYXRjaFJlamVjdEZvcm0pIHsNCiAgICAgICAgdGhpcy4kcmVmcy5iYXRjaFJlamVjdEZvcm0uY2xlYXJWYWxpZGF0ZSgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOaJuemHj+aLkue7neehruiupOaTjeS9nCAqLw0KICAgIGhhbmRsZUJhdGNoUmVqZWN0Q29uZmlybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMuYmF0Y2hSZWplY3RGb3JtLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKCF2YWxpZCkgew0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOS6jOasoeehruiupOWvueivneahhg0KICAgICAgICBjb25zdCBjdXRvZmZEYXRlID0gdGhpcy5iYXRjaFJlamVjdERpYWxvZy5mb3JtLmN1dG9mZkRhdGU7DQogICAgICAgIGNvbnN0IGNvbmZpcm1NZXNzYWdlID0gYOaCqOehruWumuimgeaLkue7nSAke2N1dG9mZkRhdGV9IOWPiuS5i+WJjeeahOaJgOacieW+heaOpeWNleW3peWNleWQl++8n+atpOaTjeS9nOWPr+mAmui/h+aXpeW/l+aBouWkjeOAgmA7DQoNCiAgICAgICAgdGhpcy4kbW9kYWwuY29uZmlybShjb25maXJtTWVzc2FnZSwgJ+aJuemHj+aLkue7neehruiupCcsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumuaJp+ihjCcsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsDQogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLA0KICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogZmFsc2UNCiAgICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5leGVjdXRlQmF0Y2hSZWplY3QoKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIC8vIOeUqOaIt+WPlua2iOaTjeS9nA0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJp+ihjOaJuemHj+aLkue7neaTjeS9nCAqLw0KICAgIGV4ZWN1dGVCYXRjaFJlamVjdCgpIHsNCiAgICAgIHRoaXMuYmF0Y2hSZWplY3REaWFsb2cubG9hZGluZyA9IHRydWU7DQoNCiAgICAgIC8vIOWwhuaXpeacn+i9rOaNouS4uuaXtumXtOaIs++8iOavq+enku+8iQ0KICAgICAgY29uc3QgY3V0b2ZmRGF0ZVN0ciA9IHRoaXMuYmF0Y2hSZWplY3REaWFsb2cuZm9ybS5jdXRvZmZEYXRlICsgJyAyMzo1OTo1OSc7DQogICAgICBjb25zdCBjdXRvZmZEYXRlID0gbmV3IERhdGUoY3V0b2ZmRGF0ZVN0cik7DQogICAgICBjb25zdCBjdXRvZmZUaW1lc3RhbXAgPSBjdXRvZmZEYXRlLmdldFRpbWUoKTsNCg0KICAgICAgY29uc3QgcmVxdWVzdERhdGEgPSB7DQogICAgICAgIGN1dG9mZkRhdGU6IGN1dG9mZlRpbWVzdGFtcA0KICAgICAgfTsNCg0KICAgICAgYmF0Y2hSZWplY3RXb3JrT3JkZXJzKHJlcXVlc3REYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5iYXRjaFJlamVjdERpYWxvZy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuaGFuZGxlQmF0Y2hSZWplY3REaWFsb2dDbG9zZSgpOw0KDQogICAgICAgIC8vIOaYvuekuuaIkOWKn+aPkOekug0KICAgICAgICBjb25zdCB7IGJhdGNoSWQsIHByb2Nlc3NlZENvdW50IH0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKGDmk43kvZzmiJDlip/vvIHmibnmrKHlj7fvvJoke2JhdGNoSWR977yM5YWx5aSE55CG5LqGICR7cHJvY2Vzc2VkQ291bnR9IOadoeW3peWNleOAgmApOw0KDQogICAgICAgIC8vIOWIt+aWsOWIl+ihqA0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy5iYXRjaFJlamVjdERpYWxvZy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIC8vIOmUmeivr+S/oeaBr+S8mueUseWFqOWxgOmUmeivr+WkhOeQhuWZqOaYvuekug0KICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/mi5Lnu53mk43kvZzlpLHotKU6JywgZXJyb3IpOw0KICAgICAgfSk7DQogICAgfSwNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqb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file": "index.vue", "sourceRoot": "src/views/insurance/workOrder", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"90px\">\r\n      <el-form-item label=\"保险类型\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择保险类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" v-if=\"(dict.value == '1' && checkPermi(['insurance:old-people-accident-insurance:query'])) || ((dict.value == '7' || dict.value == '8' || dict.value == '9') && checkPermi(['insurance:disabled-people-insurance:query']))\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\">\r\n        <el-select v-model=\"queryParams.hospitalName\" placeholder=\"请选择医院\" clearable>\r\n          <el-option\r\n            v-for=\"item in hospitalNames\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"保险公司\" v-if=\"checkPermi(['insurance:company:query'])\">\r\n        <CompanySelect v-model=\"queryParams.companyId\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"长者名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入长者名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"理赔时间\">\r\n        <el-date-picker v-model=\"dateRangeCompensatingDate\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"票据完整度\">\r\n        <el-select v-model=\"queryParams.completeStatus\" placeholder=\"请选择完整度\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"就诊时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeTreatmentDatetime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeCreateTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:work-order:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\r\n                   v-hasPermi=\"['insurance:work-order:settlement-import']\">已理赔工单导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleClaimAmountImport\"\r\n                   v-hasPermi=\"['insurance:work-order:claim-import']\">理赔金额导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" size=\"mini\" icon=\"el-icon-upload2\"\r\n                   @click=\"handleWandaImport\" :loading=\"wandaImportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:import']\">万达医疗数据更新</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document\" size=\"mini\"\r\n                   @click=\"handleBatchOperationLog\"\r\n                   v-hasPermi=\"['insurance:work-order:query']\">批量操作日志</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\"\r\n                   @click=\"handleBatchReject\"\r\n                   v-hasPermi=\"['insurance:work-order:batch-reject']\">批量拒绝旧工单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" width=\"50px\"/>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"type\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column v-if=\"checkPermi(['insurance:work-order:show-company'])\" label=\"负责公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"长者名称\" align=\"center\" prop=\"desensitizedName\" />\r\n      <el-table-column label=\"身份证号\" align=\"center\" prop=\"desensitizedIdCardNumber\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"desensitizedMobilePhoneNumber\" />\r\n      <el-table-column label=\"医院\" align=\"center\" prop=\"hospitalName\" />\r\n      <el-table-column label=\"就诊时间\" align=\"center\" prop=\"treatmentDatetime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建议理赔金额\" align=\"center\" width=\"100\" prop=\"suggestCompensatingMoney\" />\r\n      <el-table-column label=\"赔付金额\" align=\"center\" prop=\"actualMoney\" />\r\n      <el-table-column label=\"票据完整度\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS\" :value=\"scope.row.completeStatus\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"险种\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"授权状态\" align=\"center\" prop=\"authStatus\" />\r\n      <el-table-column label=\"理赔申请\" align=\"center\" prop=\"supplementaryFileRecordId\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.supplementaryFileRecordId == null\">否</el-tag>\r\n          <el-tag v-else type=\"info\">是</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"300px\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleEcard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:cert'])\">电子证照</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleOrder(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:ticket'])\">电子保单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHouseHold(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:house'])\">电子户口</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleBankCard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:bankcard'])\">银行账号</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handlerPersonInfo(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">联系方式</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDisablePerson(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">残疾人证</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleRecord(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:medical']) && scope.row.status > 1\">就诊证明</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"if(!scope.row.supplementaryFileRecordId) { handleDetail(scope.row); } else { handleDetail2(scope.row); }\"\r\n                    v-if=\"checkPermi(['insurance:work-order:query'])\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleTake(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:take']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">接单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReject(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:reject']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">拒绝</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDelay(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:delay']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">延后</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHospitalCheck(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:hospital-check']) && scope.row.status === 1 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">盖章</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleProcess(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:process']) && scope.row.status === 2 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">处理</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleVisit(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:visit']) && scope.row.status === 3 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回访</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReturn(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:return']) && (scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 6) && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回退</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleSupplementary(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">补充资料</el-button>\r\n          <el-dropdown  @command=\"(command) => handleCommand(command, scope.$index, scope.row)\"\r\n                        v-if=\"queryParams.status != null\"\r\n                        v-hasPermi=\"['insurance:work-order:update', 'insurance:work-order:delete']\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\r\n                </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDelete\" size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                                v-hasPermi=\"['insurance:work-order:delete']\">删除</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleCreatePdf\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n                                v-hasPermi=\"['insurance:work-order:update']\">创建未签章pdf</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <el-drawer :title=\"detailTitle\" :visible.sync=\"detailOpen\" direction=\"rtl\" size=\"60%\">\r\n      <WorkOrderDetail ref=\"workOrderDetail\" :opType=\"opType\" v-if=\"detailOpen\" :id=\"detailId\" />\r\n      <div class=\"drawer-footer\" v-if=\"detailTitle !== '查看详情'\">\r\n        <el-button type=\"primary\" @click=\"doAction\" :loading=\"confirmBtnLoading\">{{confirmBtnText}}</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer title=\"电子签章\" :visible.sync=\"esignOpen\" direction=\"rtl\" size=\"90%\">\r\n      <esign />\r\n      <div class=\"drawer-footer\">\r\n        <el-button type=\"primary\" @click=\"doAction\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子证照\" :visible.sync=\"ecardOpen\" width=\"500px\">\r\n      <ecard :idNum=\"ecard.idNum\" :name=\"ecard.name\" />\r\n    </el-dialog>\r\n    <el-drawer title=\"保单详情\" :visible.sync=\"orderOpen\" direction=\"rtl\" size=\"90%\">\r\n      <order :idNum=\"idNum\"/>\r\n    </el-drawer>\r\n    <el-drawer title=\"残疾人证\" :visible.sync=\"disablePerson.open\" direction=\"rtl\" size=\"90%\">\r\n      <disablePerson :idNum=\"disablePerson.idNum\"/>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子户口\" :visible.sync=\"houseHoldOpen\" width=\"550px\">\r\n      <household :idNum=\"household.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"银行账号\" :visible.sync=\"bankAccount.bankAccountOpen\" width=\"550px\">\r\n      <bankCard :idCard=\"bankAccount.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"多来源信息\" :visible.sync=\"personInfo.open\" width=\"550px\">\r\n      <personInfo :idCard=\"personInfo.idNum\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\r\n        :action=\"upload.url\" :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"补充资料\" :visible.sync=\"supplementary.open\" width=\"800px\" append-to-body>\r\n      <el-tabs v-model=\"supplementary.activeTab\">\r\n        <el-tab-pane v-for=\"(urls, type) in supplementary.files\"\r\n                     :key=\"type\"\r\n                     :label=\"getSupplementaryTypeLabel(type)\"\r\n                     :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog :title=\"'补充资料详情'\" :visible.sync=\"detailOpen2\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"居民\">{{ detailForm.residentName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">\r\n          <dict-tag :type=\"DICT_TYPE.SUPPLEMENTARY_RECORD_STATUS\" :value=\"detailForm.status\"/>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"户籍地址\">{{ detailForm.hjAddress }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{ detailForm.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"实际理赔金额\" v-if=\"detailForm.actualMoney\">\r\n          {{ detailForm.actualMoney }}元\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <el-divider content-position=\"left\">补充资料</el-divider>\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane\r\n          v-for=\"(urls, type) in fileUrlMap\"\r\n          :key=\"type\"\r\n          :label=\"getSupplementaryTypeLabel(type)\"\r\n          :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 批量操作日志抽屉 -->\r\n    <el-drawer\r\n      title=\"批量操作日志\"\r\n      :visible.sync=\"batchLogDrawer.visible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleBatchLogDrawerClose\">\r\n\r\n      <!-- 搜索条件 -->\r\n      <el-form :model=\"batchLogDrawer.queryParams\" ref=\"batchLogQueryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"操作类型\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.operationType\" placeholder=\"请选择操作类型\" clearable>\r\n            <el-option label=\"批量拒绝\" value=\"BATCH_REJECT\"></el-option>\r\n            <el-option label=\"批量恢复\" value=\"BATCH_RECOVER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作状态\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n            <el-option label=\"执行中\" value=\"RUNNING\"></el-option>\r\n            <el-option label=\"已完成\" value=\"COMPLETED\"></el-option>\r\n            <el-option label=\"执行失败\" value=\"FAILED\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作员\">\r\n          <el-input v-model=\"batchLogDrawer.queryParams.operatorName\" placeholder=\"请输入操作员姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作时间\">\r\n          <el-date-picker\r\n            v-model=\"batchLogDrawer.dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getBatchLogList\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetBatchLogQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 批量操作日志表格 -->\r\n      <el-table v-loading=\"batchLogDrawer.loading\" :data=\"batchLogDrawer.list\" style=\"width: 100%\">\r\n        <el-table-column label=\"批次号\" align=\"center\" prop=\"batchId\" width=\"180\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationTypeDisplay\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"statusDisplay\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getBatchLogStatusType(scope.row.status)\">{{ scope.row.statusDisplay }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"处理数量\" align=\"center\" prop=\"processedCount\" width=\"80\"></el-table-column>\r\n        <el-table-column label=\"操作员\" align=\"center\" prop=\"operatorName\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.endTime ? parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行时长\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDuration(scope.row.duration) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remarks\" show-overflow-tooltip></el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <pagination\r\n        v-show=\"batchLogDrawer.total > 0\"\r\n        :total=\"batchLogDrawer.total\"\r\n        :page.sync=\"batchLogDrawer.queryParams.pageNo\"\r\n        :limit.sync=\"batchLogDrawer.queryParams.pageSize\"\r\n        @pagination=\"getBatchLogList\"\r\n        style=\"margin-top: 20px;\" />\r\n    </el-drawer>\r\n\r\n    <!-- 批量拒绝模态框 -->\r\n    <el-dialog\r\n      title=\"批量拒绝历史待接单工单\"\r\n      :visible.sync=\"batchRejectDialog.visible\"\r\n      width=\"500px\"\r\n      :before-close=\"handleBatchRejectDialogClose\">\r\n\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <p style=\"color: #606266; line-height: 1.6;\">\r\n          此操作将批量拒绝指定日期及之前的所有待接单工单。被拒绝的工单状态将变更为\"行政拒绝\"，\r\n          此操作可通过批量操作日志进行恢复。请谨慎操作。\r\n        </p>\r\n      </div>\r\n\r\n      <el-form :model=\"batchRejectDialog.form\" ref=\"batchRejectForm\" label-width=\"120px\">\r\n        <el-form-item label=\"截止日期\" prop=\"cutoffDate\"\r\n                      :rules=\"[{ required: true, message: '请选择截止日期', trigger: 'change' }]\">\r\n          <el-date-picker\r\n            v-model=\"batchRejectDialog.form.cutoffDate\"\r\n            type=\"date\"\r\n            placeholder=\"选择截止日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n          <div style=\"font-size: 12px; color: #909399; margin-top: 5px;\">\r\n            将拒绝此日期及之前的所有待接单工单\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleBatchRejectDialogClose\">取 消</el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchRejectConfirm\" :loading=\"batchRejectDialog.loading\">执 行</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWorkOrder,\r\n  updateWorkOrder,\r\n  deleteWorkOrder,\r\n  getWorkOrder,\r\n  getWorkOrderPage,\r\n  exportWorkOrderExcel,\r\n  takeWorkOrder,\r\n  hospitalCheck,\r\n  process as process2,\r\n  visit,\r\n  reject,\r\n  delay,\r\n  returnWorkOrder,\r\n  getHospitalNames,\r\n  createPdf,\r\n  getBatchOperationLogPage,\r\n  batchRejectWorkOrders\r\n}\r\nfrom \"@/api/insurance/workOrder\";\r\nimport {getSupplementaryFileRecord} from \"@/api/insurance/supplementaryFileRecord\";\r\nimport ImageUpload from '@/components/ImageUpload';\r\nimport { checkPermi, checkRole } from \"@/utils/permission\";\r\nimport WorkOrderDetail from \"./detail\"\r\nimport ecard from '../components/ecard.vue';\r\nimport esign from '../components/esignature.vue';\r\nimport order from '../components/order.vue';\r\nimport disablePerson from '../components/disablePerson.vue';\r\nimport household from '../components/household.vue';\r\nimport bankCard from '../components/bankCard.vue';\r\nimport personInfo from '../components/personInfo.vue';\r\nimport CompanySelect from '../company/components/companySelect.vue';\r\nimport {getBaseHeader} from \"@/utils/request\";\r\nconst CONFIRM_TEXT = '确 定';\r\nexport default {\r\n  name: \"WorkOrder\",\r\n  components: {\r\n    ImageUpload,\r\n    WorkOrderDetail,\r\n    ecard,\r\n    esign,\r\n    order,\r\n    household,\r\n    bankCard,\r\n    personInfo,\r\n    disablePerson,\r\n    CompanySelect,\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeCompensatingDate: [],\r\n      dateRangeCreateTime: [],\r\n      dateRangeTreatmentDatetime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        type: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        treatmentSerialNumberType: this.canInitialFilter()? '1': null,\r\n        completeStatus: this.canInitialFilter()? '0': null,\r\n        hospitalName: null,\r\n        companyId: null,\r\n        types: [],\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"长者名称不能为空\", trigger: \"blur\" }],\r\n        idCardNumber: [{ required: true, message: \"身份证号不能为空\", trigger: \"blur\" }],\r\n      },\r\n      detailId: undefined,\r\n      detailTitle: undefined,\r\n      detailOpen: false,\r\n      detailOpen2: false,\r\n      method: undefined,\r\n      ecardOpen: false,\r\n      esignOpen: false,\r\n      orderOpen: false,\r\n      disablePerson: {\r\n        open: false,\r\n        idNum: undefined,\r\n      },\r\n      ecard: {\r\n        idNum: undefined,\r\n        name: undefined\r\n      },\r\n      confirmBtnText: CONFIRM_TEXT,\r\n      confirmBtnLoading: false,\r\n      houseHoldOpen: false,\r\n      household: {\r\n        idNum: undefined,\r\n      },\r\n      opType: undefined,\r\n      idNum: undefined,\r\n      bankAccount: {\r\n        bankAccountOpen: false,\r\n        idNum: undefined\r\n      },\r\n      personInfo: {\r\n        open: false,\r\n        idNum: undefined\r\n      },\r\n      hospitalNames: [],\r\n      //已赔付工单导入\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: getBaseHeader(),\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-settlement-work-order',\r\n        claimAmountUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-claim-amount-work-order',\r\n        wandaImportUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-wanda-data'\r\n      },\r\n      supplementary: {\r\n        open: false,\r\n        activeTab: '',\r\n        files: {},\r\n      },\r\n      supplementaryTypeMap: {\r\n        MEDICAL_DIAGNOSIS_PROOF: '医疗诊断证明',\r\n        MEDICAL_FEE_INVOICE: '医疗费用发票',\r\n        DRUG_LIST: '用药清单',\r\n        MEDICAL_SETTLEMENT: '医保结算单',\r\n        OUTPATIENT_MEDICAL_RECORDS: '门诊病历(门诊)',\r\n        DISCHARGE_RECORD: '出院记录(住院)',\r\n        DISABILITY_CERTIFICATE: '残疾鉴定报告',\r\n        TRAFFIC_ACCIDENT_CERTIFICATE: '交通事故责任认定书'\r\n      },\r\n      wandaImportLoading: false,\r\n      detailForm: {},\r\n      fileUrlMap: {},\r\n      activeTab: '',\r\n      // 批量操作日志抽屉\r\n      batchLogDrawer: {\r\n        visible: false,\r\n        loading: false,\r\n        total: 0,\r\n        list: [],\r\n        dateRange: [],\r\n        queryParams: {\r\n          pageNo: 1,\r\n          pageSize: 10,\r\n          operationType: null,\r\n          status: null,\r\n          operatorName: null,\r\n          beginStartTime: null,\r\n          endStartTime: null\r\n        }\r\n      },\r\n      // 批量拒绝对话框\r\n      batchRejectDialog: {\r\n        visible: false,\r\n        loading: false,\r\n        form: {\r\n          cutoffDate: null\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    if (checkPermi(['insurance:old-people-accident-insurance:query'])) {\r\n      this.queryParams.types.push(1);\r\n    }\r\n    if (checkPermi(['insurance:disabled-people-insurance:query'])) {\r\n      this.queryParams.types.push(7);\r\n      this.queryParams.types.push(8);\r\n      this.queryParams.types.push(9);\r\n    }\r\n    this.getList();\r\n    getHospitalNames().then(response => {\r\n      this.hospitalNames = response.data;\r\n    })\r\n  },\r\n  methods: {\r\n    canInitialFilter() {\r\n      // return this.checkRole(['insurance', 'mz']);\r\n      return false;\r\n    },\r\n    handleCommand(command, index, row) {\r\n      switch (command) {\r\n        case 'handleUpdate':\r\n          this.handleUpdate(row);\r\n          break;\r\n        case 'handleDelete':\r\n          this.handleDelete(row);\r\n          break;\r\n        case 'handleCreatePdf':\r\n          this.handleCreatePdf(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      this.queryParams.status = this.getStatus();\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      // 执行查询\r\n      getWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getStatus() {\r\n      return this.$route.query.status\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.detailOpen = false;\r\n      this.detailOpen2 = false;\r\n      this.esignOpen = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        address: undefined,\r\n        hospitalName: undefined,\r\n        invoice: undefined,\r\n        bill: undefined,\r\n        medicalRecord: undefined,\r\n        summary: undefined,\r\n        diagnose: undefined,\r\n        disabilityReport: undefined,\r\n        deathProof: undefined,\r\n        adviceMoney: undefined,\r\n        suggestCompensatingMoney: undefined,\r\n        actualMoney: undefined,\r\n        compensatingDate: undefined,\r\n        remark: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeCompensatingDate = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.dateRangeTreatmentDatetime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工单\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工单\";\r\n      });\r\n    },\r\n    handleCreatePdf(row) {\r\n      createPdf(row.id).then(response => {\r\n        this.getList();\r\n      });\r\n    },\r\n    handleDetail(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"查看详情\";\r\n    },\r\n    handleDetail2(row) {\r\n      getSupplementaryFileRecord(row.supplementaryFileRecordId).then(response => {\r\n        this.detailForm = response.data;\r\n        try {\r\n          this.fileUrlMap = this.detailForm.fileUrls ? JSON.parse(this.detailForm.fileUrls) : {};\r\n          // 过滤掉没有图片的类型\r\n          this.fileUrlMap = Object.fromEntries(\r\n            Object.entries(this.fileUrlMap).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.fileUrlMap).length > 0) {\r\n            // 设置第一个标签为激活状态\r\n            this.activeTab = Object.keys(this.fileUrlMap)[0];\r\n          }\r\n        } catch (e) {\r\n          console.error('解析 fileUrls 失败:', e);\r\n          this.fileUrlMap = {};\r\n        }\r\n        this.detailOpen2 = true;\r\n      });\r\n    },\r\n    doAction() {\r\n      let p = undefined;\r\n      switch(this.method) {\r\n        case 'take':\r\n          this.confirmBtnText = '正在签章,请稍候';\r\n          this.confirmBtnLoading = true;\r\n          p = takeWorkOrder(this.detailId);\r\n          break;\r\n        case 'hospital':\r\n          p = hospitalCheck(this.detailId);\r\n          break;\r\n        case 'process':\r\n          p = process2(this.detailId);\r\n          break;\r\n        case 'visit':\r\n          let submitData = this.$refs.workOrderDetail.getSubmitData()\r\n          submitData.id = this.detailId\r\n          p = visit(submitData);\r\n          break;\r\n        case 'reject':\r\n          let rejectData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = reject(rejectData);\r\n          break;\r\n        case 'delay':\r\n          let delayData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = delay(delayData);\r\n          break;\r\n        default:\r\n          console.log('找不到对应方法: ' + this.method);\r\n      }\r\n      p.then(() => {\r\n        this.confirmBtnLoading = false;\r\n        this.confirmBtnText = CONFIRM_TEXT;\r\n        this.cancel();\r\n        this.getList();\r\n      });\r\n    },\r\n    handleTake(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"接单\";\r\n      this.method = 'take';\r\n      this.confirmBtnText = '接 单';\r\n      this.opType = 'take';\r\n    },\r\n    handleReject(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"拒绝\";\r\n      this.method = 'reject';\r\n      this.confirmBtnText = '拒绝';\r\n      this.opType = 'reject';\r\n    },\r\n    handleDelay(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"延后\";\r\n      this.method = 'delay';\r\n      this.confirmBtnText = '延 后';\r\n      this.opType = 'delay';\r\n    },\r\n    handleHospitalCheck(row) {\r\n      this.detailId = row.id;\r\n      this.esignOpen = true;\r\n      this.method = 'hospital';\r\n    },\r\n    handleProcess(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"处理\";\r\n      this.method = 'process';\r\n      this.opType = 'process';\r\n\r\n    },\r\n    handleVisit(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"回访\";\r\n      this.method = 'visit';\r\n      this.opType = 'visit';\r\n    },\r\n    handleReturn(row) {\r\n      this.$modal.confirm(`是否确认回退工单(${row.name})`).then(function() {\r\n          return returnWorkOrder(row.id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"回退成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateWorkOrder(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createWorkOrder(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除工单编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteWorkOrder(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      this.addBeginAndEndTime(params, this.dateRangeTreatmentDatetime, 'treatmentDatetime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleEcard(row) {\r\n      this.ecardOpen = true;\r\n      this.ecard = {\r\n        idNum: row.idCardNumber,\r\n        name: row.name\r\n      }\r\n    },\r\n    handleOrder(row) {\r\n      this.orderOpen = true;\r\n      this.idNum = row.idCardNumber\r\n    },\r\n    handleDisablePerson(row) {\r\n      this.disablePerson.open = true;\r\n      this.disablePerson.idNum = row.idCardNumber;\r\n    },\r\n    handleRecord(row) {\r\n      window.open(row.pdf.signedPdf, \"_blank\", \"resizable,scrollbars,status\");\r\n    },\r\n    handleHouseHold(row) {\r\n      this.houseHoldOpen = true;\r\n      this.household = {\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handleBankCard(row) {\r\n      this.bankAccount = {\r\n        bankAccountOpen: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handlerPersonInfo(row) {\r\n      this.personInfo = {\r\n        open: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    checkPermi,\r\n    checkRole,\r\n    handleImport() {\r\n      this.upload.title = \"工单导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.url;\r\n    },\r\n    handleClaimAmountImport() {\r\n      this.upload.title = \"理赔金额导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.claimAmountUrl;\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      if (response.code !== 0) {\r\n        this.$modal.msgError(response.msg)\r\n        return;\r\n      }\r\n      let fileName = file.name;\r\n      let href = response.data;\r\n      let downA = document.createElement(\"a\");\r\n      downA.href = href;\r\n      downA.download = fileName;\r\n      downA.click();\r\n      window.URL.revokeObjectURL(href);\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$modal.msgSuccess(\"导入成功，请查看导入结果文件\");\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n    /** 处理补充资料按钮点击 */\r\n    handleSupplementary(row) {\r\n      // 先获取工单详情\r\n      getWorkOrder(row.id).then(response => {\r\n        const workOrder = response.data;\r\n        if (!workOrder.supplementaryFiles) {\r\n          this.$modal.msgError(\"没有补充资料\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          const files = JSON.parse(workOrder.supplementaryFiles);\r\n          // 过滤掉没有图片的类型\r\n          this.supplementary.files = Object.fromEntries(\r\n            Object.entries(files).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.supplementary.files).length === 0) {\r\n            this.$modal.msgError(\"没有补充资料\");\r\n            return;\r\n          }\r\n\r\n          // 设置第一个标签为激活状态\r\n          this.supplementary.activeTab = Object.keys(this.supplementary.files)[0];\r\n          this.supplementary.open = true;\r\n        } catch (e) {\r\n          console.error(\"解析补充资料失败\", e);\r\n          this.$modal.msgError(\"解析补充资料失败\");\r\n        }\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取工单详情失败\");\r\n      });\r\n    },\r\n    /** 获取补充资料类型的显示文本 */\r\n    getSupplementaryTypeLabel(type) {\r\n      return this.supplementaryTypeMap[type] || type;\r\n    },\r\n    /** 万达数据导入按钮操作 */\r\n    handleWandaImport() {\r\n      this.upload.title = \"万达数据导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.wandaImportUrl;\r\n    },\r\n    /** 批量操作日志按钮操作 */\r\n    handleBatchOperationLog() {\r\n      this.batchLogDrawer.visible = true;\r\n      this.getBatchLogList();\r\n    },\r\n    /** 获取批量操作日志列表 */\r\n    getBatchLogList() {\r\n      this.batchLogDrawer.loading = true;\r\n      // 处理时间范围参数\r\n      let params = { ...this.batchLogDrawer.queryParams };\r\n      if (this.batchLogDrawer.dateRange && this.batchLogDrawer.dateRange.length === 2) {\r\n        params.beginStartTime = this.batchLogDrawer.dateRange[0];\r\n        params.endStartTime = this.batchLogDrawer.dateRange[1];\r\n      }\r\n\r\n      getBatchOperationLogPage(params).then(response => {\r\n        this.batchLogDrawer.list = response.data.list;\r\n        this.batchLogDrawer.total = response.data.total;\r\n        this.batchLogDrawer.loading = false;\r\n      }).catch(() => {\r\n        this.batchLogDrawer.loading = false;\r\n      });\r\n    },\r\n    /** 重置批量操作日志查询 */\r\n    resetBatchLogQuery() {\r\n      this.batchLogDrawer.dateRange = [];\r\n      this.batchLogDrawer.queryParams = {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        operationType: null,\r\n        status: null,\r\n        operatorName: null,\r\n        beginStartTime: null,\r\n        endStartTime: null\r\n      };\r\n      this.getBatchLogList();\r\n    },\r\n    /** 关闭批量操作日志抽屉 */\r\n    handleBatchLogDrawerClose() {\r\n      this.batchLogDrawer.visible = false;\r\n    },\r\n    /** 获取批量操作状态标签类型 */\r\n    getBatchLogStatusType(status) {\r\n      switch (status) {\r\n        case 'RUNNING':\r\n          return 'warning';\r\n        case 'COMPLETED':\r\n          return 'success';\r\n        case 'FAILED':\r\n          return 'danger';\r\n        default:\r\n          return 'info';\r\n      }\r\n    },\r\n    /** 格式化执行时长 */\r\n    formatDuration(duration) {\r\n      if (!duration) return '-';\r\n\r\n      const seconds = Math.floor(duration / 1000);\r\n      const minutes = Math.floor(seconds / 60);\r\n      const hours = Math.floor(minutes / 60);\r\n\r\n      if (hours > 0) {\r\n        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;\r\n      } else if (minutes > 0) {\r\n        return `${minutes}分${seconds % 60}秒`;\r\n      } else {\r\n        return `${seconds}秒`;\r\n      }\r\n    },\r\n    /** 批量拒绝按钮操作 */\r\n    handleBatchReject() {\r\n      this.batchRejectDialog.visible = true;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n    },\r\n    /** 关闭批量拒绝对话框 */\r\n    handleBatchRejectDialogClose() {\r\n      this.batchRejectDialog.visible = false;\r\n      this.batchRejectDialog.loading = false;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n      // 清除表单验证\r\n      if (this.$refs.batchRejectForm) {\r\n        this.$refs.batchRejectForm.clearValidate();\r\n      }\r\n    },\r\n    /** 批量拒绝确认操作 */\r\n    handleBatchRejectConfirm() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n\r\n        // 二次确认对话框\r\n        const cutoffDate = this.batchRejectDialog.form.cutoffDate;\r\n        const confirmMessage = `您确定要拒绝 ${cutoffDate} 及之前的所有待接单工单吗？此操作可通过日志恢复。`;\r\n\r\n        this.$modal.confirm(confirmMessage, '批量拒绝确认', {\r\n          confirmButtonText: '确定执行',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: false\r\n        }).then(() => {\r\n          this.executeBatchReject();\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        });\r\n      });\r\n    },\r\n    /** 执行批量拒绝操作 */\r\n    executeBatchReject() {\r\n      this.batchRejectDialog.loading = true;\r\n\r\n      // 将日期转换为时间戳（毫秒）\r\n      const cutoffDateStr = this.batchRejectDialog.form.cutoffDate + ' 23:59:59';\r\n      const cutoffDate = new Date(cutoffDateStr);\r\n      const cutoffTimestamp = cutoffDate.getTime();\r\n\r\n      const requestData = {\r\n        cutoffDate: cutoffTimestamp\r\n      };\r\n\r\n      batchRejectWorkOrders(requestData).then(response => {\r\n        this.batchRejectDialog.loading = false;\r\n        this.handleBatchRejectDialogClose();\r\n\r\n        // 显示成功提示\r\n        const { batchId, processedCount } = response.data;\r\n        this.$modal.msgSuccess(`操作成功！批次号：${batchId}，共处理了 ${processedCount} 条工单。`);\r\n\r\n        // 刷新列表\r\n        this.getList();\r\n      }).catch(error => {\r\n        this.batchRejectDialog.loading = false;\r\n        // 错误信息会由全局错误处理器显示\r\n        console.error('批量拒绝操作失败:', error);\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .drawer-footer {\r\n    display: flex;\r\n    padding: 0 50px 20px;\r\n    .el-button {\r\n      flex: 1\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}